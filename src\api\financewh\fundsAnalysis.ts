import request from '/@/utils/yhrequest';
//凭证基础设置
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Financewh}/FundsAnalysis/`;

//QueryAllFundCharAnalysis 查询总资金分析
export const QueryAllFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryAllFundCharAnalysis', { params, ...config });

//QueryPlatformFundCharAnalysis 查询平台资金分析
export const QueryPlatformFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryPlatformFundCharAnalysis', { params, ...config });

//QueryCNFundCharAnalysis 查询出纳资金分析
export const QueryCNFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCNFundCharAnalysis', { params, ...config });

//QueryCashFlowCharAnalysis 查询总现金流分析
export const QueryCashFlowCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowCharAnalysis', { params, ...config });

//QueryFundsAnalysis 查询总资金分析
export const QueryFundsAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysis', { params, ...config });

//QueryFundsAnalysisByTotalAmount 查询总资金明细分析
export const QueryFundsAnalysisByTotalAmount = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByTotalAmount', { params, ...config });

//QueryFundsAnalysisByUseFullFund 查询可用资金明细分析   ----todo
export const QueryFundsAnalysisByUseFullFund = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByUseFullFund', { params, ...config });

//QueryFundsAnalysisByFreezeFund 查询冻结资金明细分析   ----todo
export const QueryFundsAnalysisByFreezeFund = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByFreezeFund', { params, ...config });

//QueryFundsAnalysisByManagerFund 查询总经办资金明细分析   ----todo
export const QueryFundsAnalysisByManagerFund = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByManagerFund', { params, ...config });

//QueryCashFlowAnalysis 查询现金流概览分析
export const QueryCashFlowAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAnalysis', { params, ...config });

//QueryCashFlowAllInAnalysis 查询现金流总收入概览分析
export const QueryCashFlowAllInAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllInAnalysis', { params, ...config });

//QueryCashFlowAllOutAnalysis 查询现金流总支出概览分析
export const QueryCashFlowAllOutAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllOutAnalysis', { params, ...config });

//QueryCashFlowPlatCashInAnalysis 查询现金流国内平台现金净流入概览分析
export const QueryCashFlowPlatCashInAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowPlatCashInAnalysis', { params, ...config });

//QueryCashFlowAllOutByPurchaseAnalysis 查询现金流采购款明细支出概览分析
export const QueryCashFlowAllOutByPurchaseAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllOutByPurchaseAnalysis', { params, ...config });

//QueryCashFlowAllOutByExpressAnalysis 查询现金流快递费明细支出概览分析
export const QueryCashFlowAllOutByExpressAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllOutByExpressAnalysis', { params, ...config });

//QueryCashFlowAllInByDate 查询现金流总收入明细
export const QueryCashFlowAllInByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllInByDate', { params, ...config });

//QueryCashFlowAllOutByDate 查询现金流总支出明细
export const QueryCashFlowAllOutByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowAllOutByDate', { params, ...config });

//QueryCashFlowPlatCashInByDate 查询现金流国内平台现金净流入明细
export const QueryCashFlowPlatCashInByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowPlatCashInByDate', { params, ...config });

//QueryFundsAnalysisByTotalAmountByDate 查询总资金明细
export const QueryFundsAnalysisByTotalAmountByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByTotalAmountByDate', { params, ...config });

//QueryFundsAnalysisByFreezeFundByDate 查询冻结资金明细
export const QueryFundsAnalysisByFreezeFundByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByFreezeFundByDate', { params, ...config });

//QueryFundsAnalysisByManagerFundByDate 查询总经办资金明细
export const QueryFundsAnalysisByManagerFundByDate = (params: any, config = {}) => request.get(apiPrefix + 'QueryFundsAnalysisByManagerFundByDate', { params, ...config });
