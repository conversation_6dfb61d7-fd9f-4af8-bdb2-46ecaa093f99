<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					v-model:startDate="query.startDate"
					v-model:endDate="query.endDate"
					startPlaceholder="下单开始时间"
					endPlaceholder="下单结束时间"
					style="width: 200px"
					:clearable="false"
				/>
				<div class="publicCss">
					<manyInput v-model:inputt="query.processNo" title="流程号" placeholder="流程号" :maxRows="100" :maxlength="3000" />
				</div>
				<div class="publicCss">
					<manyInput v-model:inputt="query.orderNo" title="订单编号" placeholder="订单编号" :maxRows="100" :maxlength="3000" />
				</div>
				<el-input v-model.trim="query.groupName" placeholder="小组" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" :id="`eachPage-${tableId}`" :tableCols="tableCols" :query="mergedQuery" :query-api="GetAllPlatformData" showsummary />
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, computed, watch, defineProps, defineExpose } from 'vue';
import { GetAllPlatformData } from '/@/api/financial/newOperation';
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
import dayjs from 'dayjs';

// 定义组件属性
interface PlatformData {
	id: number | number[];
	isJD: boolean;
}
const props = defineProps<{ platformData: PlatformData }>();

const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const query = ref({
	startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	processNo: '', //流程号
	orderNo: '', //订单编号
	groupName: '', //运营组
});

// 合并查询参数，添加平台相关参数
const mergedQuery = computed(() => {
	const baseQuery: any = { ...query.value };
	const { id } = props.platformData || ({} as PlatformData);
	if (Array.isArray(id)) {
		baseQuery.platforms = id;
	} else if (id !== undefined) {
		baseQuery.platform = id;
	}
	return baseQuery;
});

const table = ref();

// 根据平台生成唯一 table id
const tableId = computed(() => {
	const { id } = props.platformData;
	return Array.isArray(id) ? id.join(',') : String(id);
});

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
};

// 暴露方法给父组件调用
defineExpose({
	getList,
});

// 基础表格列
const baseTableCols = [
	{ width: '120', sortable: true, field: 'processNo', title: '流程号', align: 'center' },
	{ width: '110', sortable: true, field: 'groupName', title: '小组', align: 'center' },
	{ width: '110', sortable: true, field: 'orderDate', title: '下单日期', align: 'center', formatter: 'formatDate' },
	{ width: '110', sortable: true, field: 'refundDate', title: '返款日期', align: 'center', formatter: 'formatDate' },
	{ width: '110', sortable: true, field: 'orderNo', title: '订单编号', align: 'center' },
	{ width: '80', sortable: true, field: 'amounted', title: '金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '80', sortable: true, field: 'commission', title: '佣金', align: 'right', formatter: 'fmtAmt2' },
];

// 快递费用列（仅京东平台显示）
const courierFeeCol = { width: '110', sortable: true, field: 'courierFee', title: '快递费用', align: 'right', formatter: 'fmtAmt2' };

// 其他列
const otherCols = [
	{ width: '160', sortable: true, field: 'shopName', title: '店铺', align: 'center' },
	{ width: '200', sortable: true, field: 'proName', title: '商品', align: 'center' },
	{ width: '110', sortable: true, field: 'sendOutGood', title: '发货', align: 'center' },
	{ width: '110', sortable: true, field: 'proCode', title: 'ID', align: 'center' },
	{ width: 'auto', sortable: true, field: 'notes', title: '备注', align: 'center' },
	{ width: '110', sortable: true, field: 'brand', title: '品牌', align: 'center' },
];

// 动态计算表格列
const tableCols = computed<any>(() => {
	// 如果是京东平台，添加快递费用列
	if (props.platformData && props.platformData.isJD) {
		return [...baseTableCols, courierFeeCol, ...otherCols];
	} else {
		return [...baseTableCols, ...otherCols];
	}
});
</script>

<style scoped lang="scss"></style>
