<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" :clearable="false"
					class="publicCss" style="width: 200px" />
        <dataRange v-model:startDate="query.openStartTime" v-model:endDate="query.openEndTime" :clearable="false"
					class="publicCss" style="width: 260px" startPlaceholder="开票查询开始时间" endPlaceholder="开票查询结束时间" />
				<el-input v-model.trim="query.businessId" class="publicCss" placeholder="采购单流程号" clearable
					maxlength="50" />
				<el-input v-model.trim="query.commitBusinessId" class="publicCss" placeholder="税费流程号" clearable
					maxlength="50" />
				<el-input v-model.trim="query.sponsor" class="publicCss" placeholder="发起人姓名" clearable maxlength="50" />
				<el-input v-model.trim="query.billHeader" class="publicCss" placeholder="开票抬头" clearable
					maxlength="50" />
				<el-input v-model.trim="query.vendor" class="publicCss" placeholder="供应商名称" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsName" class="publicCss" placeholder="商品名称" clearable
					maxlength="50" />
				<el-input v-model.trim="query.accountName" class="publicCss" placeholder="账户名称" clearable
					maxlength="50" />
				<el-input v-model.trim="query.account" class="publicCss" placeholder="银行账号" clearable maxlength="50" />
				<el-select v-model="query.taxPayType" placeholder="税费付款方式" class="publicCss" clearable>
					<el-option label="税费私转" value="税费私转" />
					<el-option label="税费公转" value="税费公转" />
					<el-option label="货款含税费" value="货款含税费" />
				</el-select>
				<el-select v-model="query.autoCommit" placeholder="自动提交税费" class="publicCss" clearable>
					<el-option label="是" value="是" />
					<el-option label="否" value="否" />
				</el-select>
				<el-select v-model="query.commitStatus" placeholder="审批状态" class="publicCss" clearable>
					<el-option label="待提交" value="待提交" />
					<el-option label="已审核" value="已审核" />
					<el-option label="已拒绝" value="已拒绝" />
				</el-select>
				<el-select v-model="query.editStatus" placeholder="实际开票状态" class="publicCss" clearable>
					<el-option label="已添加" value="已添加" />
					<el-option label="待添加" value="待添加" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="synchronous" type="primary">同步</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202410261350" :tableCols="tableCols" isNeedDisposeProps
				@disposeProps="disposeProps" isNeedCheckBox @select="checkboxChange" :query="query"
				:queryApi="QueryTaxData" showsummary>
				<template #toolbar_buttons>
					<el-button @click="onCombinedSubmit" type="primary">合并提交</el-button>
					<el-button @click="onSubmit" type="primary">提交</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<el-button @click="onModifiedState" type="primary" :disabled="checkState">修改审批状态</el-button>
					<el-button @click="onBatchDownload" type="primary">批量下载</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editPriceVisible" title="实际开票金额" width="20%" draggable overflow :close-on-click-modal="false">
		<div style="display: flex; width: 100%">
			<el-form :model="singForm" :rules="rules" ref="ruleFormRef" label-width="110px" width="150px">
				<el-form-item label="开票日期">
					<dataRange v-model:date="singForm.editTime" :clearable="false" class="publicCss"
						placeholder="请选择开票日期" style="width: 200px" type="date" />
				</el-form-item>
				<el-form-item label="开票金额">
					<el-input-number v-model.trim="singForm.actualBillAmount" placeholder="请输入" :min="0" :max="9999999"
						:precision="2" :controls="false" />
				</el-form-item>
				<el-form-item label="票据">
					<uploadMf v-if="editPriceVisible" v-model:imagesStr="singForm.billImgs" uploadName="上传PDF文件"
						uploadFormat=".pdf" :upstyle="{ height: 40, width: 40 }" :limit="1"></uploadMf>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="singForm.remark" placeholder="请输入备注" type="textarea" autocomplete="off" clearable
						maxlength="200" show-word-limit :autosize="{ minRows: 3, maxRows: 3 }" resize="none" />
				</el-form-item>
			</el-form>
		</div>
		<div style="display: flex; justify-content: center; margin-top: 20px">
			<el-button @click="editPriceVisible = false">取消</el-button>
			<el-button type="primary" @click="onSortSave(ruleFormRef)" v-reclick="1000">保存</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import axios from 'axios';
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { QueryTaxData, UpdateTaxData, CommitTaxData, MergeCommitTaxData, ExportTaxData, GetTaxDataBusinessId, BatchUpdateTaxDataCommitStatus } from '/@/api/cwManager/taxData';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';

import JSZip from 'jszip';
import { saveAs } from 'file-saver';

const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const options = ref<Public.options[]>([]);
const checkboxList = ref([]);
const checkboxContent = ref([]);
const valArr = ref([]);
const ruleFormRef = ref<FormInstance>();
const editPriceVisible = ref(false);
const checkState = ref(false);
const loading = ref(false);
const table = ref();
const query = ref({
	startTime: dayjs().startOf('month').format('YYYY-MM-DD'),
	endTime: dayjs().endOf('month').format('YYYY-MM-DD'),
  openStartTime: '',
  openEndTime: '',
	businessId: '',
	sponsor: '',
	billHeader: '',
	vendor: '',
	goodsName: '',
	accountName: '',
	account: '',
	taxPayType: '',
	autoCommit: '',
	commitStatus: '',
	editStatus: '',
	commitBusinessId: '',
});

const singForm = ref({
	actualBillAmount: '',
	billImgs: [],
	editTime: '',
	remark: '',
});

const rules = {
	actualBillAmount: [{ required: true, message: '请输入开票金额', trigger: 'blur' }],
	billImgs: [{ required: true, message: '请上传票据', trigger: 'change' }],
	editTime: [{ required: true, message: '请选择开票日期', trigger: 'change' }],
};

const exportProps = async () => {
	await ExportTaxData({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

interface FileItem {
	name: string;
	blob: Blob;
}

const fileslist = ref<FileItem[]>([]);



const downzip = async () => {
	const zip = new JSZip();
	const currentDate = new Date();
	const year = currentDate.getFullYear();
	const month = String(currentDate.getMonth() + 1).padStart(2, '0');
	const day = String(currentDate.getDate()).padStart(2, '0');
	const formattedDate = `${year}${month}${day}`;
	fileslist.value.forEach((file) => {
		zip.file('pdf文件集合' + '/' + file.name + '.pdf', file.blob, { binary: true });
	});
	zip.generateAsync({ type: 'blob' }).then((content) => {
		saveAs(content, `pdf文件集合.zip`);
	});
	loading.value = false;
	return;
};

const onBatchDownload = async () => {
    if (checkboxContent.value.length === 0) return ElMessage.error('请选择数据');
    await getListFile();

    await downzip();
};

const getListFile = async() => {
    fileslist.value = [];
    loading.value = true;
    let billImgs = checkboxContent.value
        .filter((item1: any) => item1.billImgs != '')
        .map((item: any) => {
            return { billImgs: item.billImgs, businessId: item.businessId };
        });



    await Promise.all(billImgs.map(async (item) => {
            if (!item.billImgs) {
                return;
            }
            await downloadFile(item.billImgs, item.businessId);
    }))
};

const downloadFile = async(url: string, name: any) => {
    try {
        let response = await fetch(url);
        if(response.status != 200){
            return;
        }
        let blob = await response.blob();
        fileslist.value.push({ name: name, blob: blob });
    } catch (error) {
    }
};

const onModifiedState = () => {
	let message = '';
	if (valArr.value.length === 1) {
		if (valArr.value[0] === '待提交') {
			message = '状态将变成为“已审核” 是否更改？';
		} else if (valArr.value[0] === '已审核') {
			message = '状态将变成为“待提交” 是否更改？';
		}
	} else {
		message = '状态不一致，无法更改';
	}
	if (message) {
		ElMessageBox.confirm(message, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}).then(async () => {
			const params = {
				ids: checkboxList.value,
				commitStatus: valArr.value[0] === '待提交' ? '已审核' : '待提交',
			};
			let { success } = await BatchUpdateTaxDataCommitStatus(params);
			if (!success) return;
			getList();
			ElMessage.success('修改成功');
		});
	}
};
//编辑
const handleEdit = (row: any) => {
	clear();
	singForm.value = JSON.parse(JSON.stringify(row));
	singForm.value.billImgs = row.billImgs ? row.billImgs.split(',') : [];
	editPriceVisible.value = true;
};

//清空数据
const clear = () => {
	singForm.value = {
		actualBillAmount: '',
		billImgs: [],
		editTime: '',
		remark: '',
	};
};

//复选框选中事件
const checkboxChange = (val: any) => {
	checkboxContent.value = val;
	checkboxList.value = val.map((item: any) => item.id);
	let valArrList = val.map((item: any) => {
		return item.commitStatus;
	});
	valArrList = Array.from(new Set(valArrList));
	if (valArrList.length > 1) {
		checkState.value = true;
	} else if (valArrList.includes('已拒绝')) {
		checkState.value = true;
	} else {
		checkState.value = false;
	}
	valArr.value = valArrList;
};

//合并提交
const onCombinedSubmit = async () => {
	if (checkboxList.value.length === 0) return ElMessage.error('请选择数据');
	ElMessageBox.confirm('是否合并提交选中数据?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		let { success } = await MergeCommitTaxData({ ids: checkboxList.value });
		if (success) {
			ElMessage.success('合并提交成功');
			getList();
		}
	});
};

//提交
const onSubmit = async () => {
	if (checkboxList.value.length === 0) return ElMessage.error('请选择数据');
	ElMessageBox.confirm('是否提交选中数据?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		let { success } = await CommitTaxData({ ids: checkboxList.value });
		if (success) {
			ElMessage.success('提交成功');
			getList();
		}
	});
};

//编辑保存
const onSortSave = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			let billImgs = singForm.value.billImgs.join(',');
			let { success } = await UpdateTaxData({ ...singForm.value, billImgs });
			if (success) {
				ElMessage.success('保存成功');
				editPriceVisible.value = false;
				clear();
				getList();
			}
		} else {
			ElMessage.error('请填写完整信息');
		}
	});
};
const synchronous = async () => {
	let { success } = await GetTaxDataBusinessId();
	if (success) {
		window.$message.success('同步成功');
		getList();
	}
};

const onDownload = (row: any) => {
	window.open(row.billImgs);
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'payTime', title: '支付日期', width: '90' },
	{ sortable: true, field: 'commitStatus', title: '审批状态', width: '90' },
	{ sortable: true, field: 'businessId', title: '采购单流程号', width: '120' },
	{ sortable: true, field: 'businessTitle', title: '流程名称', width: '90' },
	{ sortable: true, field: 'sponsor', title: '发起人姓名', width: '120' },
	{ sortable: true, field: 'payAmount', title: '付款金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'commitBusinessId', title: '税费流程号', width: '120' },
	{ sortable: true, field: 'billHeader', title: '开票抬头', width: '90' },
	{ sortable: true, field: 'goodsName', title: '商品名称', width: '90' },
	{ sortable: true, field: 'vendor', title: '系统供应商名称', width: '130' },
	{ sortable: true, field: 'accountName', title: '账户名称', width: '90' },
	{ sortable: true, field: 'account', title: '银行账号', width: '90' },
	{ sortable: true, field: 'accountBank', title: '开户行', width: '90' },
	{ sortable: true, field: 'taxPayType', title: '税费付款方式', width: '120' },
	{ sortable: true, field: 'goodsAmount', title: '货款金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'taxes', title: '税费', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'taxesRate', title: '税费占比', width: '90' },
	{ sortable: true, field: 'billAmount', title: '应开票金额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'editTime', title: '开票日期', width: '90', formatter: 'formatDate' },
	{ sortable: true, field: 'actualBillAmount', title: '实际开票金额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'discrepancy', title: '差异', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'taxAccountName', title: '税费私转账户', width: '120' },
	{ sortable: true, field: 'taxAccount', title: '税费账户', width: '90' },
	{ sortable: true, field: 'taxAccountBank', title: '税费私转开户行', width: '130' },
	{ sortable: true, field: 'autoCommit', title: '是否自动提交税费', width: '140' },
	{ sortable: true, field: 'editStatus', title: '实际开票状态', width: '120' },
	{
		title: 'pdf文件',
		width: '75',
		fixed: 'right',
		align: 'center',
		type: 'btnList',
		btnList: [{ title: '下载', handle: onDownload, isDisabled: (row) => !row.billImgs }],
	},
	{ sortable: true, field: 'remark', title: '备注', width: '120' },
	// { sortable: true, field: 'billImgs', title: '票据', width: '90', type: 'image' },
	{
		title: '操作',
		width: '50',
		fixed: 'right',
		align: 'center',
		type: 'btnList',
		field:'**************',
		btnList: [{ title: '编辑', handle: handleEdit }],
	},
]);

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.payTime = dayjs(item.payTime).format('YYYY-MM-DD');
	});
	callback(data);
};
</script>

<style scoped lang="scss">
.publicCss {
	width: 140px;
}
</style>
