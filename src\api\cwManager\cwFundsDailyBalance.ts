import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/CwFundsDailyBalance/`;

// 拼多多
// 财务资金-拼多多-查询贷款余额
export const GetPddDailyBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDailyBalance', params, config);

// 财务资金-拼多多-查询贷款余额明细
export const GetPddDailyBalanceDetail = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDailyBalanceDetail', params, config);

// 财务资金-拼多多-查询营销账户
export const GetPddMarketingAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetPddMarketingAccount', params, config);

// 财务资金-拼多多-查询营销账户明细
export const GetPddMarketingAccountDetail = (params: any, config = {}) => request.post(apiPrefix + 'GetPddMarketingAccountDetail', params, config);

// 财务资金-拼多多-查询保证金账户
export const GetPddDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDepositAccount', params, config);

// 财务资金-拼多多-查询保证金账户明细
export const GetPddDepositAccountDetail = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDepositAccountDetail', params, config);

// 财务资金-拼多多-导出保证金账户
export const ExportPddDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPddDepositAccount', params, config);

// 财务资金-拼多多-导出明细保证金账户
export const ExportPddDepositAccountDetail = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPddDepositAccountDetail', params, config);

// 财务资金-拼多多-导出营销账户
export const ExportPddMarketingAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPddMarketingAccount', params, config);

// 天猫
// 财务资金-天猫-查询天猫货款余额
export const GetTMGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetTMGoodsBalance', params, config);

// 财务资金-天猫-查询天猫推广账户
export const GetTMPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTMPromotionAccount', params, config);

// 财务资金-天猫-查询天猫保证金账户
export const GetTMDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTMDepositAccount', params, config);

// 财务资金-天猫-导出保证金账户
export const ExportTMDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTMDepositAccount', params, config);

// 财务资金-天猫-导出营销账户
export const ExportTMPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTMPromotionAccount', params, config);

// 淘宝
// 财务资金-淘宝-查询淘宝货款余额
export const GetTBGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetTBGoodsBalance', params, config);

// 财务资金-淘宝-查询淘宝推广账户
export const GetTBPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTBPromotionAccount', params, config);

// 财务资金-淘宝-查询淘宝保证金账户
export const GetTBDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTBDepositAccount', params, config);

// 财务资金-淘宝-导出保证金账户
export const ExportTBDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTBDepositAccount', params, config);

// 财务资金-淘宝-导出营销账户
export const ExportTBPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTBPromotionAccount', params, config);

// 抖音
// 财务资金-抖音-查询抖音货款余额
export const GetDYGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetDYGoodsBalance', params, config);

// 财务资金-抖音-查询抖音千川账户
export const GetDYQianChuanAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetDYQianChuanAccount', params, config);

// 财务资金-抖音-查询抖音其他账户
export const GetDYOtherAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetDYOtherAccount', params, config);

// 财务资金-抖音-导出保证金账户
export const ExportDYOtherAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportDYOtherAccount', params, config);

// 财务资金-抖音-导出营销账户
export const ExportDYQianChuanAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportDYQianChuanAccount', params, config);

// 扣点
// 财务资金-扣点设置-查询扣点设置
export const GetFeeSetting = (params: any, config = {}) => request.post(apiPrefix + 'GetFeeSetting', params, config);

// 财务资金-扣点设置-查询扣点设置历史
export const GetFeeSettingHistory = (params: any, config = {}) => request.post(apiPrefix + 'GetFeeSettingHistory', params, config);

// 财务资金-扣点设置-编辑扣点设置项目
export const EditFeeSetting = (params: any, config = {}) => request.post(apiPrefix + 'EditFeeSetting', params, config);

// 财务资金-扣点设置-新增扣点设置项目
export const SetFeeSetting = (params: any, config = {}) => request.post(apiPrefix + 'SetFeeSetting', params, config);

// 财务资金-扣点设置-删除扣点设置
export const DeleteFeeSetting = (params: any, config = {}) => request.post(apiPrefix + 'DeleteFeeSetting', params, config);

//拼多多货款账户导出 ExportPddDailyBalance
export const ExportPddDailyBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPddDailyBalance', params, config);

//拼多多货款账户明细导出 ExportPddDailyBalanceDetail
export const ExportPddDailyBalanceDetail = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPddDailyBalanceDetail', params, config);

//ExportTMGoodsBalance
export const ExportTMGoodsBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTMGoodsBalance', params, config);

//ExportTBGoodsBalance
export const ExportTBGoodsBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTBGoodsBalance', params, config);

//ExportDYGoodsBalance
export const ExportDYGoodsBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportDYGoodsBalance', params, config);

// 京东
// 财务资金-京东-查询京东货款账户
export const GetJDDailyBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetJDDailyBalance', params, config);

// 财务资金-京东-查询京东推广账户
export const GetJDPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetJDPromotionAccount', params, config);

// 财务资金-京东-查询京东保证金账户
export const GetJDDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetJDDepositAccount', params, config);

// 财务资金-京东-导出京东货款账户明细
export const ExportJDDailyBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportJDDailyBalance', params, config);

// 财务资金-京东-导出京东保证金账户
export const ExportJDDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportJDDepositAccount', params, config);

// 财务资金-京东-导出京东营销账户
export const ExportJDPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportJDPromotionAccount', params, config);

// 快手
// 财务资金-快手-查询快手货款账户
export const GetKSGoodsAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetKSGoodsAccount', params, config);

// 财务资金-快手-查询快手保证金账户
export const GetKSDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetKSDepositAccount', params, config);

// 财务资金-快手-查询快手营销账户
export const GetKSPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetKSPromotionAccount', params, config);

// 财务资金-快手-导出快手货款账户明细
export const ExportKSGoodsAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportKSGoodsAccount', params, config);

// 财务资金-快手-导出快手保证金账户
export const ExportKSDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportKSDepositAccount', params, config);

// 财务资金-快手-导出快手营销账户
export const ExportKSPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportKSPromotionAccount', params, config);

// 视频号
// 财务资金-视频号-查询视频号货款账户
export const GetSPHGoodsAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetSPHGoodsAccount', params, config);

// 财务资金-视频号-查询视频号保证金账户
export const GetSPHDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetSPHDepositAccount', params, config);

// 财务资金-视频号-导出视频号货款账户明细
export const ExportSPHGoodsAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportSPHGoodsAccount', params, config);

// 财务资金-视频号-导出视频号保证金账户
export const ExportSPHDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportSPHDepositAccount', params, config);

// 小红书
// 财务资金-小红书-查询小红书货款账户
export const GetXHSGoodsAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetXHSGoodsAccount', params, config);

// 财务资金-小红书-查询小红书保证金账户
export const GetXHSDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetXHSDepositAccount', params, config);

// 财务资金-小红书-导出小红书货款账户明细
export const ExportXHSGoodsAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportXHSGoodsAccount', params, config);

// 财务资金-小红书-导出小红书保证金账户
export const ExportXHSDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportXHSDepositAccount', params, config);

// 淘工厂
// 财务资金-淘工厂-查询淘工厂货款账户
export const GetTGCDailyAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCDailyAccount', params, config);

// 财务资金-淘工厂-查询淘工厂保证金账户
export const GetTGCDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCDepositAccount', params, config);

// 财务资金-淘工厂-查询淘工厂营销账户
export const GetTGCPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCPromotionAccount', params, config);

// 财务资金-淘工厂-导出淘工厂货款账户明细
export const ExportTGCDailyAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTGCDailyAccount', params, config);

// 财务资金-淘工厂-导出淘工厂保证金账户
export const ExportTGCDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTGCDepositAccount', params, config);

// 财务资金-淘工厂-导出淘工厂营销账户
export const ExportTGCPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTGCPromotionAccount', params, config);

// 阿里巴巴
// 财务资金-阿里巴巴-查询阿里巴巴货款账户
export const GetAliGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetAliGoodsBalance', params, config);

// 财务资金-阿里巴巴-查询阿里巴巴保证金账户
export const GetAliDepositAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetAliDepositAccount', params, config);

// 财务资金-阿里巴巴-查询阿里巴巴营销账户
export const GetAliPromotionAccount = (params: any, config = {}) => request.post(apiPrefix + 'GetAliPromotionAccount', params, config);

// 财务资金-阿里巴巴-导出阿里巴巴货款账户明细
export const ExportAliGoodsBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAliGoodsBalance', params, config);

// 财务资金-阿里巴巴-导出阿里巴巴保证金账户
export const ExportAliDepositAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAliDepositAccount', params, config);

// 财务资金-阿里巴巴-导出阿里巴巴营销账户
export const ExportAliPromotionAccount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAliPromotionAccount', params, config);

// 财务资金-锁定操作
export const LockDailyBalanceData = (params: any, config = {}) => request.post(apiPrefix + 'LockDailyBalanceData', params, config);

// 财务资金-解锁操作
export const UnLockDailyBalanceData = (params: any, config = {}) => request.post(apiPrefix + 'UnLockDailyBalanceData', params, config);

// 其他资产-查询
export const GetOtherAsset = (params: any, config = {}) => request.post(apiPrefix + 'GetOtherAsset', params, config);

// 其他资产-导出
export const ExportOtherAsset = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportOtherAsset', params, config);

// 其他资产-编辑
export const EditOtherAsset = (params: any, config = {}) => request.post(apiPrefix + 'EditOtherAsset', params, config);

// 仓储资产-查询
export const GetWarehouseAsset = (params: any, config = {}) => request.post(apiPrefix + 'GetWarehouseAsset', params, config);

// 仓储资产-导出
export const ExportWarehouseAsset = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportWarehouseAsset', params, config);

// 拼多多货款账户汇总趋势图
export const GetPddDailyBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDailyBalanceTotalMap', params, config);

// 拼多多营销账户汇总趋势图
export const GetPddMarketingAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetPddMarketingAccountTotalMap', params, config);

// 拼多多保证金账户汇总趋势图
export const GetPddDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetPddDepositAccountTotalMap', params, config);

// 天猫货款账户汇总趋势图
export const GetTMGoodsBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTMGoodsBalanceTotalMap', params, config);

// 天猫营销账户汇总趋势图
export const GetTMPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTMPromotionAccountTotalMap', params, config);

// 天猫保证金账户汇总趋势图
export const GetTMDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTMDepositAccountTotalMap', params, config);

// 淘宝货款账户汇总趋势图
export const GetTBGoodsBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTBGoodsBalanceTotalMap', params, config);

// 淘宝营销账户汇总趋势图
export const GetTBPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTBPromotionAccountTotalMap', params, config);

// 淘宝保证金账户汇总趋势图
export const GetTBDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTBDepositAccountTotalMap', params, config);

// 抖音货款账户汇总趋势图
export const GetDYGoodsBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetDYGoodsBalanceTotalMap', params, config);

// 抖音营销账户汇总趋势图
export const GetDYQianChuanAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetDYQianChuanAccountTotalMap', params, config);

// 抖音保证金账户汇总趋势图
export const GetDYOtherAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetDYOtherAccountTotalMap', params, config);

// 淘工厂货款账户汇总趋势图
export const GetTGCDailyAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCDailyAccountTotalMap', params, config);

// 淘工厂营销账户汇总趋势图
export const GetTGCPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCPromotionAccountTotalMap', params, config);

// 淘工厂保证金账户汇总趋势图
export const GetTGCDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetTGCDepositAccountTotalMap', params, config);

// 京东货款账户汇总趋势图
export const GetJDDailyBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetJDDailyBalanceTotalMap', params, config);

// 京东营销账户汇总趋势图
export const GetJDPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetJDPromotionAccountTotalMap', params, config);

// 京东保证金账户汇总趋势图
export const GetJDDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetJDDepositAccountTotalMap', params, config);

// 快手货款账户汇总趋势图
export const GetKSGoodsAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetKSGoodsAccountTotalMap', params, config);

// 快手营销账户汇总趋势图
export const GetKSPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetKSPromotionAccountTotalMap', params, config);

// 快手保证金账户汇总趋势图
export const GetKSDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetKSDepositAccountTotalMap', params, config);

// 视频号货款账户汇总趋势图
export const GetSPHGoodsAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetSPHGoodsAccountTotalMap', params, config);

// 视频号保证金账户汇总趋势图
export const GetSPHDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetSPHDepositAccountTotalMap', params, config);

// 阿里巴巴货款账户汇总趋势图
export const GetAliGoodsBalanceTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetAliGoodsBalanceTotalMap', params, config);

// 阿里巴巴营销账户汇总趋势图
export const GetAliPromotionAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetAliPromotionAccountTotalMap', params, config);

// 阿里巴巴保证金账户汇总趋势图
export const GetAliDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetAliDepositAccountTotalMap', params, config);

// 小红书货款账户汇总趋势图
export const GetXHSGoodsAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetXHSGoodsAccountTotalMap', params, config);

// 小红书保证金账户汇总趋势图
export const GetXHSDepositAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetXHSDepositAccountTotalMap', params, config);

// 拼多多货款账户编辑
export const EditPddDailyBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditPddDailyBalance', params, config);

// 天猫货款账户编辑
export const EditTMGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditTMGoodsBalance', params, config);

// 淘宝货款账户编辑
export const EditTBGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditTBGoodsBalance', params, config);

// 抖音货款账户编辑
export const EditDYGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditDYGoodsBalance', params, config);

// 淘工厂货款账户编辑
export const EditTGCGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditTGCGoodsBalance', params, config);

// 京东货款账户编辑
export const EditJDGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditJDGoodsBalance', params, config);

// 快手货款账户编辑
export const EditKSGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditKSGoodsBalance', params, config);

// 视频号货款账户编辑
export const EditSPHGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditSPHGoodsBalance', params, config);

// 阿里巴巴货款账户编辑
export const EditAliGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditAliGoodsBalance', params, config);

// 小红书货款账户编辑
export const EditXHSGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditXHSGoodsBalance', params, config);

// 分销
// 财务资金-分销-查询分销
export const GetFXDailyBalance = (params: any, config = {}) => request.post(apiPrefix + 'GetFXDailyBalance', params, config);

// 财务资金-分销-导出分销
export const ExportFXDailyBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportFXDailyBalance', params, config);

// 财务资金-分销-编辑
export const EditFXGoodsBalance = (params: any, config = {}) => request.post(apiPrefix + 'EditFXGoodsBalance', params, config);

// 财务资金-分销-趋势图
export const GetFXGoodsAccountTotalMap = (params: any, config = {}) => request.post(apiPrefix + 'GetFXGoodsAccountTotalMap', params, config);

//手动初始化平台店铺数据
export const InitShopData = (params: any, config = {}) => request.post(apiPrefix + 'InitShopData', params, config);

//手动获取提现
export const SyncShopDataWithDrawAmount = (params: any, config = {}) => request.post(apiPrefix + 'SyncShopDataWithDrawAmount', params, config);

//手动验算
export const CheckShopDataDailyBalance = (params: any, config = {}) => request.post(apiPrefix + 'CheckShopDataDailyBalance', params, config);

//辅助核算查询
export const GetFuZhuCheck = (params: any, config = {}) => request.post(apiPrefix + 'GetFuZhuCheck', params, config);

//辅助核算导出
export const ExportFuZhuCheck = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportFuZhuCheck', params, config);

//辅助核算编辑
export const EditFuZhuCheck = (params: any, config = {}) => request.post(apiPrefix + 'EditFuZhuCheck', params, config);

//辅助核算其他数据查询
export const GetFuZhuCheckOtherData = (params: any, config = {}) => request.post(apiPrefix + 'GetFuZhuCheckOtherData', params, config);

//保存辅助核算
export const SaveFuZhuCheckOtherData = (params: any, config = {}) => request.post(apiPrefix + 'SaveFuZhuCheckOtherData', params, config);

//删除辅助核算
export const DelFuZhuCheckOtherData = (params: any, config = {}) => request.post(apiPrefix + 'DelFuZhuCheckOtherData', params, config);
