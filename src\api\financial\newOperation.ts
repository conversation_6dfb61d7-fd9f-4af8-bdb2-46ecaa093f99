import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Financial}/NewOperation/`;

//刷单流程自动化-查询
export const GetSummaryVariousPlatformsList = (params: any, config = {}) => request.post(apiPrefix + 'GetSummaryVariousPlatformsList', params, config);

//刷单流程自动化-编辑
export const EditSummaryVariousPlatforms = (params: any, config = {}) => request.post(apiPrefix + 'EditSummaryVariousPlatforms', params, config);

//刷单流程自动化-导出
export const ExportSummaryVariousPlatforms = (params: any, config = {}) => request.post(apiPrefix + 'ExportSummaryVariousPlatforms', params, config);

//刷单流程自动化-各平台查询
export const GetAllPlatformData = (params: any, config = {}) => request.post(apiPrefix + 'GetAllPlatformData', params, config);
