<template>
	<Container style="width: 100%; height: 100%" v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end" style="width: 240px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="initProps">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<lineChart
				style="height: 350px"
				v-if="!loading"
				:chartData="trendChart.analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '350px',
					'box-sizing': 'border-box',
					'line-height': '350px',
				}"
				@onLegendMethod="onLegendMethod"
				@onBarClick="onBarClick"
				:lengendObject="lengendObject"
				:clickConfig="{
					enabled: true,
					nameField: 'seriesName',
					targetValues: ['总收入', '总支出'],
				}"
			/>
			<div class="analysis-boxes-container">
				<div class="analysis-box" v-for="(config, index) in boxConfigs" :key="index">
					<div class="info-panel">
						<div class="panel-header">
							<span class="panel-title">{{ formatReceiptDate(config.data.receiptDate) || query.end }}{{ config.title }}:</span>
							<span class="panel-total" @click="openDetailDialog(config)" :style="{ cursor: 'pointer' }">{{ (onTotalingMethod(config.data) || 0).toLocaleString() }}</span>
						</div>
						<div class="panel-divider"></div>
						<div class="panel-content">
							<div class="content-item" v-for="(item, key) in config.items" :key="key">
								<span class="item-label" :class="{ 'clickable-label': isClickableField(item.field) }" @click="handleItemClick(item, config)">
									{{ item.label }}
								</span>
								<span class="item-value">{{ (config.data[item.field] || 0).toLocaleString() }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 详情对话框 -->
			<el-dialog
				v-model="detailDialog.visible"
				:title="detailDialog.title"
				width="70%"
				draggable
				overflow
				@close="closeDetailDialog"
				class="fund-detail-dialog"
				:class="`fund-detail-dialog--${detailDialog.type}`"
			>
				<div class="fund-detail-content">
					<div class="fund-detail-chart" v-loading="detailDialog.loading">
						<lineChart
							v-if="detailDialog.visible && detailDialog.chartData"
							:chartData="detailDialog.chartData"
							ref="detailChart"
							:thisStyle="{
								width: '100%',
								height: '600px',
								'box-sizing': 'border-box',
								'line-height': '600px',
							}"
							:gridStyle="{
								top: '15%',
								left: '8%',
								right: '4%',
								bottom: '12%',
								containLabel: true,
							}"
							@onLegendMethod="onDetailLegendMethod"
							:lengendObject="detailDialog.legendObject"
							class="fund-detail-line-chart"
						/>
					</div>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { onMounted, ref, computed, defineAsyncComponent, nextTick } from 'vue';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import {
	QueryCashFlowAnalysis,
	QueryCashFlowAllInByDate,
	QueryCashFlowAllOutByDate,
	QueryCashFlowPlatCashInByDate,
	QueryCashFlowAllInAnalysis,
	QueryCashFlowAllOutAnalysis,
	QueryCashFlowPlatCashInAnalysis,
	QueryCashFlowAllOutByPurchaseAnalysis,
	QueryCashFlowAllOutByExpressAnalysis,
} from '/@/api/financewh/fundsAnalysis';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import dayjs from 'dayjs';
const query = ref({
	start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
	end: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
});
import { decimal } from '/@/utils/decimal';

const sumChart = ref();
const detailChart = ref();
const loading = ref(true);
const lengendObject = ref({});

const analysisData5 = ref<any>({});
const analysisData6 = ref<any>({});
const analysisData7 = ref<any>({});

// 盒子配置
const boxConfigs = computed(() => [
	{
		title: '总收入',
		data: analysisData5.value,
		items: [
			{ label: '国内平台现金净流入', field: 'platInAmount' },
			{ label: '独立核算单位转入', field: 'independentInAmount' },
			{ label: '采购款退款', field: 'purchaseRefundInAmount' },
			{ label: '快递理赔', field: 'expressClaimsInAmount' },
			{ label: '其他收入', field: 'otherInAmount' },
		],
	},
	{
		title: '总支出',
		data: analysisData6.value,
		items: [
			{ label: '采购款:(现付、月结、采购+运营代拍)', field: 'purchaseOutAmount' },
			{ label: '快递费(面单、续重费、面单+续重费)', field: 'expressOutAmount' },
			{ label: '营销费用', field: 'marketExpOutAmount' },
			{ label: '独立核算费用', field: 'independentOutAmount' },
			{ label: '国内日常支出', field: 'dailyOut' },
		],
	},
	{
		title: '国内平台现金净流入',
		data: analysisData7.value,
		items: [
			{ label: '淘系', field: 'txCashInAmount' },
			{ label: '拼多多', field: 'pddCashInAmount' },
			{ label: '抖音', field: 'dyCashInAmount' },
			{ label: '京东', field: 'jdCashInAmount' },
			{ label: '快手', field: 'ksCashInAmount' },
			{ label: '视频号', field: 'sphCashInAmount' },
			{ label: '其他平台', field: 'otherCashInAmount' },
			{ label: '分销', field: 'fxCashInAmount' },
		],
	},
]);

// 详情对话框数据
const detailDialog = ref({
	visible: false,
	title: '',
	type: '', // 'total', 'freeze', 'manager', 'usable'
	loading: false,
	chartData: null,
	legendObject: {},
	dateRange: {
		start: '',
		end: '',
	},
});

const excludedKeys = ['receiptDate'];
const onTotalingMethod = (Subject: Record<string, any>) => {
	let total = 0;
	for (const key in Subject) {
		if (Object.prototype.hasOwnProperty.call(Subject, key)) {
			if (excludedKeys.includes(key)) continue;
			const value = Number(Subject[key]);
			if (!isNaN(value)) {
				total += Number(decimal(value, 0, 2, '+'));
			}
		}
	}
	return Number(total.toFixed(2));
};

const formatReceiptDate = (receiptDate: any) => {
	if (!receiptDate) return '';
	return dayjs(receiptDate, 'YYYYMMDD').format('YYYY-MM-DD');
};

const onLegendMethod = (val: any) => {
	lengendObject.value = val;
};

// 详情对话框图例方法
const onDetailLegendMethod = (val: any) => {
	detailDialog.value.legendObject = val;
};

// 判断是否为可点击字段
const isClickableField = (field: string) => {
	return Object.keys(fieldApiMap).includes(field);
};

// 处理字段点击事件
const handleItemClick = async (item: any, config: any) => {
	const field = item.field;

	// 检查是否为可点击字段
	if (!isClickableField(field)) {
		return;
	}

	const api = fieldApiMap[field];
	const title = fieldTitleMap[field];

	if (!api || !title) {
		return;
	}

	detailDialog.value.title = title;
	detailDialog.value.visible = true;
	detailDialog.value.loading = true;

	try {
		const params = {
			start: query.value.start,
			end: query.value.end,
		};
		const res = await api(params);
		detailDialog.value.chartData = res;

		nextTick(() => {
			if (detailChart.value) {
				detailChart.value.reSetChart(res);
			}
		});
	} catch (error) {
		console.error('加载字段明细数据失败:', error);
	} finally {
		detailDialog.value.loading = false;
	}
};

// 打开明细弹窗
const openDetailDialog = async (config: any) => {
	const api = detailApiMap[config.title];
	if (!api) {
		console.warn('未找到对应的API:', config.title);
		return;
	}

	detailDialog.value.title = `${config.title}明细`;
	detailDialog.value.visible = true;
	detailDialog.value.loading = true;

	try {
		const params = {
			start: query.value.start,
			end: query.value.end,
		};
		const res = await api(params);
		detailDialog.value.chartData = res;

		nextTick(() => {
			if (detailChart.value) {
				detailChart.value.reSetChart(res);
			}
		});
	} catch (error) {
		console.error('加载明细数据失败:', error);
	} finally {
		detailDialog.value.loading = false;
	}
};

// 明细API映射
const detailApiMap: Record<string, any> = {
	总收入: QueryCashFlowAllInAnalysis,
	总支出: QueryCashFlowAllOutAnalysis,
	国内平台现金净流入: QueryCashFlowPlatCashInAnalysis,
};

// 字段API映射
const fieldApiMap: Record<string, any> = {
	purchaseOutAmount: QueryCashFlowAllOutByPurchaseAnalysis,
	expressOutAmount: QueryCashFlowAllOutByExpressAnalysis,
};

// 字段标题映射
const fieldTitleMap: Record<string, string> = {
	purchaseOutAmount: '采购款明细',
	expressOutAmount: '快递费明细',
};

// 柱状图点击事件处理
const onBarClick = async (clickData: any) => {
	const clickedName = clickData.name;
	const clickedDataIndex = clickData.dataIndex;

	// 检查是否是我们要处理的系列（总收入或总支出）
	if (!['总收入', '总支出'].includes(clickedName)) {
		return;
	}

	// 获取点击位置对应的x轴值
	const xAxisData = trendChart.value.analysisData?.xAxis;
	if (!xAxisData || !xAxisData[clickedDataIndex]) {
		return;
	}

	const clickedDate = xAxisData[clickedDataIndex];
	// 将日期从 YYYYMMDD 格式转换为 YYYY-MM-DD 格式
	const formattedDate = dayjs(clickedDate, 'YYYYMMDD').format('YYYY-MM-DD');

	// 重新调用数据获取方法刷新面板数据，传入点击的日期
	try {
		await Promise.all([getProp5(formattedDate), getProp6(formattedDate), getProp7(formattedDate)]);
	} catch (error) {
		console.error('刷新面板数据失败:', error);
	}
};

// 关闭详情对话框
const closeDetailDialog = () => {
	detailDialog.value.visible = false;
	detailDialog.value.chartData = null;
	detailDialog.value.legendObject = {};
};
const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

const getProp = async () => {
	const res = await QueryCashFlowAnalysis(query.value);
	trendChart.value.analysisData = res;
	trendChart.value.analysisData?.series.forEach((item: any) => {
		if (item.type == 'line') {
			item.smooth = true;
		}
	});
	trendChart.value.analysisData.title = '现金流概览';
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};

const getProp5 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryCashFlowAllInByDate({ date: dateToUse });
		if (res) {
			analysisData5.value = res;
		} else {
			analysisData5.value = {
				platInAmount: 0,
				independentInAmount: 0,
				purchaseRefundInAmount: 0,
				expressClaimsInAmount: 0,
				otherInAmount: 0,
			};
		}
	} catch (error) {
		console.error('获取总收入数据失败:', error);
	}
};
const getProp6 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryCashFlowAllOutByDate({ date: dateToUse });
		if (res) {
			analysisData6.value = res;
		} else {
			analysisData6.value = {
				purchaseOutAmount: 0,
				expressOutAmount: 0,
				marketExpOutAmount: 0,
				independentOutAmount: 0,
				dailyOut: 0,
			};
		}
	} catch (error) {
		console.error('获取总支出数据失败:', error);
	}
};
const getProp7 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryCashFlowPlatCashInByDate({ date: dateToUse });
		if (res) {
			analysisData7.value = res;
		} else {
			analysisData7.value = {
				txCashInAmount: 0,
				pddCashInAmount: 0,
				dyCashInAmount: 0,
				jdCashInAmount: 0,
				ksCashInAmount: 0,
				sphCashInAmount: 0,
				otherCashInAmount: 0,
				fxCashInAmount: 0,
			};
		}
	} catch (error) {
		console.error('获取平台现金流入数据失败:', error);
	}
};

const initProps = () => {
	loading.value = true;
	Promise.all([getProp(), getProp5(), getProp6(), getProp7()])
		.then(() => {
			loading.value = false;
		})
		.finally(() => {
			loading.value = false;
		});
};
onMounted(async () => {
	initProps();
});
</script>

<style scoped lang="scss">
#main {
	width: 100%;
	height: 100%;
}

// 资金详情对话框样式
.fund-detail-dialog {
	:deep(.el-dialog__header) {
		padding: 20px 20px 10px;
		border-bottom: 1px solid #ebeef5;
	}

	:deep(.el-dialog__body) {
		padding: 20px;
	}

	// 不同类型的对话框可以有不同的样式
	&.fund-detail-dialog--total {
		:deep(.el-dialog__header) {
			background-color: #f0f9ff;
		}
	}

	&.fund-detail-dialog--freeze {
		:deep(.el-dialog__header) {
			background-color: #fef3f2;
		}
	}

	&.fund-detail-dialog--manager {
		:deep(.el-dialog__header) {
			background-color: #f6ffed;
		}
	}

	&.fund-detail-dialog--usable {
		:deep(.el-dialog__header) {
			background-color: #fff7e6;
		}
	}
}

.fund-detail-content {
	width: 100%;

	.fund-detail-header {
		margin-bottom: 20px;
		padding-bottom: 15px;
		border-bottom: 1px solid #f0f0f0;

		.fund-detail-date-picker {
			:deep(.el-date-editor) {
				width: 100%;
			}
		}
	}

	.fund-detail-chart {
		width: 100%;
		min-height: 600px;

		.fund-detail-line-chart {
			width: 100%;
			height: 100%;
		}
	}
}

.title_Css {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
}

// 三个水平排列盒子的样式
.analysis-boxes-container {
	display: flex;
	flex-direction: row;
	gap: 20px;
	height: 100%;
	padding: 20px 0;
	overflow-x: auto;
}

.analysis-box {
	display: flex;
	flex-direction: column;
	flex: 1;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
		transform: translateY(-2px);
	}

	.info-panel {
		background-color: #ffffff;
		color: #333333;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		&:hover {
			background-color: #b0cceb;
			color: #000000;
		}

		.panel-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 20px 10px;
			flex-shrink: 0;
			background-color: inherit;

			.panel-title {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				line-height: 1.4;
				flex-shrink: 0;
			}

			.panel-total {
				font-size: 16px;
				font-weight: 600;
				color: #409eff;
				transition: all 0.3s ease;

				&:hover {
					color: #1890ff;
					transform: scale(1.05);
				}
			}
		}

		.panel-divider {
			height: 1px;
			background-color: #e4e7ed;
			margin: 0 20px 8px;
			flex-shrink: 0;
		}

		.panel-content {
			flex: 1;
			overflow-y: auto;
			padding: 0 20px 16px;
			max-height: 290px;
			height: 290px;

			.content-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;
				line-height: 1.4;

				&:last-child {
					margin-bottom: 0;
				}

				.item-label {
					font-weight: 600;
					color: #333333;
					flex-shrink: 0;

					&.clickable-label {
						color: #409eff;
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							color: #1890ff;
							text-decoration: underline;
						}
					}
				}

				.item-value {
					font-weight: normal;
					color: #666666;
					text-align: right;
				}
			}
		}
	}

	.chart-panel {
		background-color: #ffffff;
		padding: 16px;
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;

		.no-data {
			color: #999999;
			font-size: 14px;
			text-align: center;
		}
	}
}
</style>
