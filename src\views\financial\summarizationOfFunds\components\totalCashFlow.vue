<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end"
					style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20250313094053" :tableCols="tableCols" showsummary isIndexFixed :query="query"
				:query-api="PageCashFlow" :export-api="ExportCashFlow" :asyncExport="{ title: '现金流', isAsync: false }"
				@footerCellClick="onSummaryTotalMap">
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="trendChart.totalMapVisible" width="60%" draggable overflow @close="lengendObject = {}">
		<div>
			<dataRange v-model:startDate="trendChart.start" v-model:endDate="trendChart.end" :clearable="false"
				startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 260px" @change="onTrendChartMethod()" />
			<lineChart v-if="trendChart.totalMapVisible" :chartData="trendChart.analysisData" ref="sumChart" :thisStyle="{
				width: '100%',
				height: '600px',
				'box-sizing': 'border-box',
				'line-height': '600px',
			}" @onLegendMethod="onLegendMethod" :lengendObject="lengendObject"/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, nextTick } from 'vue';
import { PageCashFlow, ExportCashFlow } from '/@/api/financewh/funds';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { QueryCashFlowCharAnalysis } from '/@/api/financewh/fundsAnalysis';
const query = ref({
	start: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
	platform: null,
});
const lengendObject = ref({});
const table = ref();
const sumChart = ref(); //总资产趋势图组件
const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

const onLegendMethod = (val: any) => {
  lengendObject.value = val
};

const onSummaryTotalMap = async (row: any, column: any) => {
	trendChart.value.columns = column;
	trendChart.value.end = query.value.end;
	trendChart.value.start = dayjs(trendChart.value.end).subtract(30, 'day').format('YYYY-MM-DD');
	onTrendChartMethod();
};

const onTrendChartMethod = async () => {
	const res = await QueryCashFlowCharAnalysis({
		start: trendChart.value.start,
		end: trendChart.value.end,
		column: trendChart.value.columns,
	});
	trendChart.value.analysisData = res;
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'receiptDate', title: '日期', width: '90', formatter: 'formatDate' },
	{ summaryEvent: true, sortable: true, field: 'zAmount_In', align: 'right', title: '货款合计', width: '110', formatter: 'fmtAmt0', tipmesg: '各平台“货款收入”合计+分销现金净流入' },
	{
		title: '淘系',
		align: 'center',
		field: '20250608094232',
		children: [
			{ summaryEvent: true, sortable: true, field: 'tx_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Hk_Amount_In', title: '货款收入', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Fee_Amount_Out', title: '费用支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '支出+提现' },
		],
	},
	{
		title: '拼多多',
		align: 'center',
		field: '20250608094246',
		children: [
			{ summaryEvent: true, sortable: true, field: 'pdd_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Hk_Amount_In', title: '货款收入', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Fee_Amount_Out', title: '费用支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '支出+提现' },
		],
	},
	{
		title: '抖音',
		field: '2025608094259',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'dy_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Hk_Amount_In', title: '货款收入', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Fee_Amount_Out', title: '费用支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '支出+提现' },
		],
	},
	{
		title: '京东',
		field: '20250608094311',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'jd_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Hk_Amount_In', title: '货款收入', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Fee_Amount_Out', title: '费用支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '支出+提现' },
		],
	},
	{
		title: '快手、小红书、视频号',
		field: '20250608094322',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'kxS_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_Hk_Amount_In', title: '货款收入', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_Fee_Amount_Out', title: '费用支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'kxS_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '支出+提现' },
		],
	},
	{
		title: '分销',
		field: '2025608094330',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'fx_Amount_In', title: '总收入', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'fx_Amount_Out', title: '总支出', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'fx_WithdrawAmount', title: '总提现', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'fx_Cash_In', title: '现金净流入', width: '115', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{ summaryEvent: true, sortable: true, field: 'otherPlatInAmont', title: '其他平台转入', width: '115', formatter: 'fmtAmt0', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'independInAmont', title: '独立核算单位转入', width: '115', formatter: 'fmtAmt0', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'z_Cash_In', title: '平台净流入合计', width: '140', formatter: 'fmtAmt0', align: 'right', tipmesg: '各平台“现金净流入”合计+其他平台转入+独立核算单位转入' },
	{
		title: '出纳',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'cN_Amount_In', title: '收入', width: '70', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'cN_Amount_Out', title: '支出', width: '70', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{ summaryEvent: true, sortable: true, field: 'diff_Cash', title: '现金流差额', width: '115', align: 'right', formatter: 'fmtAmt0', tipmesg: '平台现金流入合计+出纳资金收入-出纳资金支出' },
	{ summaryEvent: true, sortable: true, field: 'diff_Amount', title: '总资金差额', width: '115', align: 'right', formatter: 'fmtAmt0', tipmesg: '今日总资金-昨日总资金' },
	{ summaryEvent: true, sortable: true, field: 'amount_Check', title: '验算', width: '80', align: 'right', formatter: 'fmtAmt0', tipmesg: '现金流净差额-总资金差额' },
	{ sortable: true, field: 'remark', title: '备注', width: '80', align: 'right' },
	{ sortable: true, field: 'updateTime', title: '更新时间', width: '140', align: 'right', formatter: 'formatTime' },
]);
</script>

<style scoped lang="scss"></style>
