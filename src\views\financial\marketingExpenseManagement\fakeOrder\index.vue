<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%" @tab-change="handleTabChange">
				<el-tab-pane label="汇总" name="first" style="height: 100%">
					<summaryPage />
				</el-tab-pane>
				<el-tab-pane v-for="(platform, index) in platforms" :key="index" :label="platform.label" :name="'platform-' + index" style="height: 100%" lazy>
					<eachPage
						:platform-data="getPlatformData(platform)"
						:ref="
							(el) => {
								if (el) pageRefs[index] = el;
							}
						"
					/>
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, computed, onMounted } from 'vue';
const summaryPage = defineAsyncComponent(() => import('./components/summaryPage.vue'));
const eachPage = defineAsyncComponent(() => import('./components/eachPage.vue'));
const activeName = ref('first');
const platforms = ref([
	{ id: [1, 8, 9], label: '淘系' },
	{ id: 7, label: '京东', isJD: true },
	{ id: 10, label: '苏宁' },
	{ id: 2, label: '拼多多' },
	{ id: 6, label: '抖音' },
	{ id: 25, label: '得物' },
	{ id: 20, label: '视频号' },
	{ id: 4, label: '1688' },
	{ id: 3, label: '跨境' },
]);

// 当前选中的平台索引
const currentPlatformIndex = ref(-1);

// 存储每个平台页面的引用
const pageRefs = ref<any[]>([]);

// 处理标签页切换
const handleTabChange = (tabName: string) => {
	if (tabName.startsWith('platform-')) {
		const index = parseInt(tabName.split('-')[1]);
		currentPlatformIndex.value = index;
		// 如果是已经加载过的页签，调用其 getList 方法刷新数据
		if (pageRefs.value[index]) {
			// 使用 nextTick 确保组件已完全渲染
			setTimeout(() => {
				if (pageRefs.value[index] && pageRefs.value[index].getList) {
					pageRefs.value[index].getList();
				}
			}, 0);
		}
	} else {
		currentPlatformIndex.value = -1;
	}
};

// 初始化页面引用数组
onMounted(() => {
	// 初始化页面引用数组，长度与平台列表相同
	pageRefs.value = new Array(platforms.value.length);
});

// 获取平台数据，根据平台类型返回不同的参数
interface PlatformData {
	id: number | number[];
	label: string;
	isJD?: boolean;
}
const getPlatformData = (platform: PlatformData) => {
	return {
		id: platform.id,
		isJD: !!platform.isJD,
	};
};
</script>
