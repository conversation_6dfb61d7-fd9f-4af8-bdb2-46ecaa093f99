<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-date-picker v-model="query.dateTime" type="date" placeholder="日期" style="width: 120px" class="publicCss" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" />
				<el-input v-model.trim="query.processNo" placeholder="流程号" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.presenter" placeholder="提交人" clearable maxlength="50" class="publicCss" />
				<!--<el-select v-model="query.groupId" placeholder="运营组" class="publicCss" clearable filterable>
					<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select> -->
				<el-input v-model.trim="query.groupName" placeholder="小组" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content v-loading="loading">
			<vxetable ref="table" id="fakeOrder202508030828" :tableCols="tableCols" :query="query" :query-api="GetSummaryVariousPlatformsList" showsummary />

			<el-dialog v-model="editVisible" title="编辑" width="400" draggable overflow>
				<div style="padding: 15px 0">
					<el-form :model="singleform" :rules="singlerules">
						<el-form-item label="备注" label-width="50px">
							<el-input
								v-model="singleform.remark"
								placeholder="请输入备注"
								type="textarea"
								autocomplete="off"
								clearable
								maxlength="100"
								show-word-limit
								:autosize="{ minRows: 5, maxRows: 5 }"
								resize="none"
							/>
						</el-form-item>
					</el-form>
				</div>
				<template #footer>
					<div class="dialog-footer">
						<el-button @click="editVisible = false">取消</el-button>
						<el-button type="primary" @click="onSingleSave"> 确定 </el-button>
					</div>
				</template>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetSummaryVariousPlatformsList, ExportSummaryVariousPlatforms, EditSummaryVariousPlatforms } from '/@/api/financial/newOperation';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import { GetDirectorGroupList } from '/@/api/operatemanage/shop';
const groupList = ref<any[]>([]);
const time = dayjs().format('YYYY-MM-DD');
const query = ref({
	dateTime: time, //日期
	processNo: '', //流程号
	presenter: '', //提交人
	groupName: '', //运营组
});

const table = ref();
const loading = ref(false);

// 编辑相关变量
const editVisible = ref(false);
const singleform = ref({
	id: '',
	remark: '',
});
const singlerules = ref({});

const exportProps = async () => {
	loading.value = true;
	await ExportSummaryVariousPlatforms({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			window.$message[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功,稍后请到下载管理查看' : '导出失败'));
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
};

const handleEdit = (row: any) => {
	// 回显数据
	singleform.value = {
		id: row.id,
		remark: row.remark || '',
	};
	editVisible.value = true;
};

// 保存编辑
const onSingleSave = async () => {
	try {
		loading.value = true;
		const { success, msg } = await EditSummaryVariousPlatforms({
			id: singleform.value.id,
			remark: singleform.value.remark,
		});

		if (success) {
			window.$message.success(msg || '保存成功');
			editVisible.value = false;
			getList(); // 刷新列表
		} else {
			window.$message.error(msg || '保存失败');
		}
	} catch (error) {
		window.$message.error('保存失败');
	} finally {
		loading.value = false;
	}
};

const tableCols = ref<VxeTable.Columns[]>([
	{ width: '120', sortable: true, field: 'dateTime', title: '日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'presenter', title: '提交人', align: 'center' },
	{ width: '180', sortable: true, field: 'processNo', title: '流程号', align: 'center' },
	{ width: '120', sortable: true, field: 'groupName', title: '小组', align: 'center' },
	{ width: '120', sortable: true, field: 'amount', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_TX', title: '淘系', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_JD', title: '京东', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_SN', title: '苏宁', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_PDD', title: '拼多多', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_DY', title: '抖音', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_DW', title: '得物', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_Video', title: '视频号', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_MZ', title: '喵住', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_1688', title: '1688', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_KJ', title: '跨境', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_XH', title: '小号', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'amount_PP', title: '品牌', align: 'right', formatter: 'fmtAmt2' },
	{ width: '200', sortable: false, field: 'remark', title: '备注', align: 'center' },
	{
		title: '操作',
		align: 'center',
		width: '110',
		type: 'btnList',
		minWidth: '110',
		field: 'operationAcc',
		btnList: [{ title: '编辑', handle: (row) => handleEdit(row) }],
		fixed: 'right',
	},
]);

onMounted(async () => {
	let { data, success } = await GetDirectorGroupList();
	if (data && success) {
		groupList.value = data.map((item: any) => {
			return { value: item.key, label: item.value };
		});
	}
});
</script>

<style scoped lang="scss"></style>
