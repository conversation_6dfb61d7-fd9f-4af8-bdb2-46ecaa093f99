<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<!-- <faceSheetSettings v-model:value="query.region" title="区域" :multiple="false" class="publicCss" /> -->
				<el-input v-model.trim="query.region" placeholder="区域" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.status" class="publicCss" placeholder="状态" clearable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.instanceId" placeholder="流程编号" clearable maxlength="50" class="publicCss" />
				<shopSelect v-model:label="query.shopName" field="shopName" class="publicCss" placeholder="店铺名称" clearable filterable />
				<el-input v-model.trim="query.shopCode" placeholder="店铺编码" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.initiateUserName" placeholder="发起人" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.reviewUserName" placeholder="审核人" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.reviewStatus" class="publicCss" placeholder="审核状态" clearable>
					<el-option v-for="item in reviewList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content v-loading="loading">
			<vxetable ref="table" id="directTrain202507271436" :tableCols="tableCols" :query="query" :query-api="GetCwMarketCostInitiateReviewList" showsummary isNeedCheckBox @select="onCheckBoxMethod">
				<template #toolbar_buttons>
					<div style="color: red">注：列表拉取的日期为昨天</div>
				</template>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetCwMarketCostInitiateReviewList, ExportCwMarketCostInitiateReviewList, CwMarketCostInitiateReview } from '/@/api/cwManager/marketCostInitiate';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
const faceSheetSettings = defineAsyncComponent(() => import('/@/components/yhCom/faceSheetSettings.vue'));
const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const query = ref({
	startDate: time, //开始时间
	endDate: time, //结束时间
	status: null, //状态
	reviewStatus: null, //审核状态
	instanceId: '', //流程编号
	shopName: '', //店铺名称
	shopCode: '', //店铺编码
	initiateUserName: '', //发起人
	reviewUserName: '', //审核人
	region: '', //区域
});

const statusList = ref<Public.options[]>([
	{ label: '未发起', value: 0 },
	{ label: '待发起', value: 1 },
	{ label: '已发起', value: 2 },
	{ label: '已拒绝', value: 5 },
	{ label: '已撤销', value: 6 },
]);
const reviewList = ref<Public.options[]>([
	{ label: '待审核', value: 1 },
	{ label: '已审核', value: 2 },
	{ label: '已拒绝', value: 3 },
]);
const table = ref();
const loading = ref(false);
const checkBoxList = ref<any[]>([]);

const exportProps = async () => {
	loading.value = true;
	await ExportCwMarketCostInitiateReviewList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			window.$message[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功,稍后请到下载管理查看' : '导出失败'));
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
};

const handleReview = (row: any, val: Number) => {
	ElMessageBox.prompt(`是否审核${val == 2 ? '通过' : '拒绝'}？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		inputPlaceholder: '可输入审核备注（最多200字）',
		inputValidator: (value) => {
			if (value && value.length > 200) return '输入内容不能超过200字符';
			return true;
		},
		inputErrorMessage: '输入内容不符合要求',
		type: 'warning',
	})
		.then(async ({ value }) => {
			// 确保截取前200字符
			const trimmedValue = value ? value.substring(0, 200) : '';
			const { success } = await CwMarketCostInitiateReview({
				checkMarketCostIds: [row.id],
				reviewRemark: trimmedValue,
				reviewStatus: val,
			});
			if (success) {
				ElMessage({ type: 'success', message: `审核${val == 2 ? '通过' : '拒绝'}成功` });
				getList();
			}
		})
		.catch(() => {});
};

const onCheckBoxMethod = (val: any) => {
	checkBoxList.value = val;
};

const tableCols = ref<VxeTable.Columns[]>([
	{ width: '100', sortable: true, field: 'region', title: '区域', align: 'center' },
	{ width: '100', sortable: true, field: 'pullDate', title: '拉取日期', align: 'center', formatter: 'formatDate' },
	{ width: '80', sortable: true, field: 'status', title: '状态', align: 'center', formatter: (row: any) => statusList.value.find((item) => item.value === row.status)?.label },
	{ width: '230', sortable: true, field: 'businessId', title: '流程编号', align: 'center' },
	{ width: '132', sortable: true, field: 'payTime', title: '支付时间', align: 'center' },
	{ width: '220', sortable: true, field: 'shopName', title: '店铺名称', align: 'center' },
	{ width: '120', sortable: true, field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '120', sortable: true, field: 'shopId', title: '店铺ID', align: 'center' },
	{ width: '120', sortable: true, field: 'payPrice', title: '支付金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'tranRemark', title: '交易摘要', align: 'center' },
	{ width: '100', field: 'payPicture', title: '支付截图', align: 'center', type: 'image' },
	{ width: '100', field: 'shopPicture', title: '店铺截图', align: 'center', type: 'image' },
	{ width: '120', sortable: true, field: 'initiateUserName', title: '发起人', align: 'center' },
	{ width: '132', sortable: true, field: 'initiateTime', title: '发起时间', align: 'center' },
	{ width: '90', sortable: true, field: 'reviewStatus', title: '审核状态', align: 'center', formatter: (row: any) => reviewList.value.find((item) => item.value === row.reviewStatus)?.label },
	{ width: '120', sortable: true, field: 'reviewUserName', title: '审核人', align: 'center' },
	{ width: '132', sortable: true, field: 'reviewTime', title: '审核时间', align: 'center' },
	{ width: '200', sortable: false, field: 'reviewRemark', title: '初审备注', align: 'center' },
	{ width: '200', sortable: false, field: 'operationRemark', title: '运营备注', align: 'center' },
	{
		title: '操作',
		align: 'center',
		width: '110',
		type: 'btnList',
		minWidth: '110',
		field: 'operationAcc',
		btnList: [
			{ title: '审核通过', handle: (row) => handleReview(row, 2), isDisabled: (row: any) => row.reviewStatus !== 1 },
			{ title: '审核拒绝', handle: (row) => handleReview(row, 3), isDisabled: (row: any) => row.reviewStatus !== 1 },
		],
		fixed: 'right',
	},
]);
</script>

<style scoped lang="scss"></style>
