<template>
	<div style="height: 100%; width: 100%; padding-left: 10px; overflow: auto">
		<div v-if="chartData && chartData.series && chartData.series != null && chartData.series.length > 0" :id="'lineChart' + random" :style="props.thisStyle"></div>
		<div v-else>没有可展示的图表!</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineProps, nextTick, defineExpose } from 'vue';
import * as echarts from 'echarts';
const emit = defineEmits(['onLegendMethod', 'onBarClick']);
const random = ref('');
const props = defineProps({
	thisStyle: {
		type: Object,
		default: function () {
			return {
				width: '100%',
				height: '550px',
				'box-sizing': 'border-box',
				'line-height': '360px',
			};
		},
	},
	chartData: {
		type: Object,
		default: () => {
			return {};
		},
	},
	gridStyle: {
		type: Object,
		default: function () {
			return {
				top: '20%',
				left: '10%',
				right: '4%',
				bottom: '6%',
				containLabel: true,
			};
		},
	},
	// 默认勾选图例
	lengendObject: {
		type: Object,
		default: () => {
			return {};
		},
	},
	tooltipFormatter: {
		type: Function,
		default: null,
	},
	// 点击事件配置
	clickConfig: {
		type: Object,
		default: () => {
			return {
				enabled: false, // 是否启用点击事件
				nameField: 'name', // 要匹配的字段名，默认为 'name'
				targetValues: [], // 要点击的项的值数组，如 ['总资金', '冻结资金']
			};
		},
	},
});
const chatProps = ref({
	legend: [],
	xAxis: [],
	series: [],
	title: '',
});
const option = ref({});
var selectedLegend: Record<string, boolean> = {};
if (props.chartData.selectedLegend) {
	props.chartData.legend.forEach((f: any) => {
		if (!props.chartData.selectedLegend.includes(f)) selectedLegend[f] = false;
	});
}
const getCharts = (val: any) => {
	// 数据预处理和验证，确保所有必要字段都存在
	const chartData = {
		...val,
		legend: val?.legend || [],
		selectedLegend: Array.isArray(val?.selectedLegend) ? val.selectedLegend : [],
		series: val?.series || [],
		xAxis: val?.xAxis || [],
		yAxis: val?.yAxis || [],
		title: val?.title || '',
	};

	option.value = {
		title: {
			text: chartData.title ? chartData.title : '',
		},
		tooltip: {
			trigger: 'axis',
			formatter: props.tooltipFormatter || undefined,
		},
		legend: (() => {
			const legendConfig: any = {
				top: 30,
				data: chartData.legend,
			};

			// 优先使用 props.lengendObject（兼容之前的功能）
			if (props.lengendObject && Object.keys(props.lengendObject).length > 0) {
				legendConfig.selected = props.lengendObject;
			}
			// 如果没有 lengendObject，则使用 selectedLegend 或 全局的 selectedLegend
			else if (chartData.selectedLegend.length > 0 && chartData.legend.length > 0) {
				const selected: Record<string, boolean> = {};

				// 首先将所有图例设为不选中
				chartData.legend.forEach((item: string) => {
					if (item && typeof item === 'string') {
						selected[item] = false;
					}
				});

				// 然后将 selectedLegend 中的图例设为选中
				chartData.selectedLegend.forEach((item: string) => {
					if (item && typeof item === 'string' && chartData.legend.includes(item)) {
						selected[item] = true;
					}
				});

				// 只有当 selected 对象不为空时才设置 selected 属性
				if (Object.keys(selected).length > 0) {
					legendConfig.selected = selected;
				}
			}
			// 最后使用全局的 selectedLegend 作为备选
			else if (Object.keys(selectedLegend).length > 0) {
				legendConfig.selected = selectedLegend;
			}

			return legendConfig;
		})(),
		toolbox: {
			show: true,
			feature: {
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {},
			},
		},
		xAxis: {
			type: 'category',
			data: chartData.xAxis,
		},
		yAxis:
			chartData.yAxis.length > 0
				? chartData.yAxis.map((axis: any) => ({
						...axis,
						type: 'value',
						axisLabel: {
							formatter: (value: number) => {
								if (axis.name?.includes('百分比')) {
									return `${value}%`;
								}
								const absValue = Math.abs(value);
								if (absValue >= 10000) {
									return `${(value / 10000).toFixed(1)}万`;
								}
								return value;
							},
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
							},
						},
					}))
				: {
						type: 'value',
						axisLabel: {
							formatter: function (value: number) {
								const absValue = Math.abs(value);
								if (absValue >= 10000) {
									return (value / 10000).toFixed(1) + '万';
								}
								return value;
							},
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
							},
						},
					},
		grid: props.gridStyle,
		series: chartData.series,
	};
};
const reSetChart = (val: any) => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);
	myChart && myChart.dispose();
	getCharts(val);
	createChart();
};
const createChart = () => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);
	option.value && myChart.setOption(option.value);
	myChart.on('legendselectchanged', function (event: any) {
		console.log('Clicked legend: ' + event.name); //当前点击图例
		console.log('Selected legends: ', event.selected); //所有图例
		// 过滤出值为false的项
		const selectedTrueItems = Object.keys(event.selected)
			.filter((key) => event.selected[key] === false)
			.reduce(
				(obj, key) => {
					obj[key] = event.selected[key];
					return obj;
				},
				{} as Record<string, boolean>
			);
		emit('onLegendMethod', selectedTrueItems || {});
	});

	// 添加柱状图点击事件
	if (props.clickConfig.enabled) {
		myChart.on('click', function (params: any) {
			// 检查是否是我们要监听的项
			const fieldValue = params[props.clickConfig.nameField];
			if (props.clickConfig.targetValues.includes(fieldValue)) {
				// 发送点击事件到父组件
				emit('onBarClick', {
					name: fieldValue,
					value: params.value,
					data: params.data,
					seriesName: params.seriesName,
					dataIndex: params.dataIndex,
					allParams: params,
				});
			}
		});
	}
	window.addEventListener('resize', () => {
		myChart.resize();
	});
};
onMounted(() => {
	var e = 10;
	var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
		a = t.length,
		n = '';
	for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
	random.value = n;
	nextTick(() => {
		chatProps.value = JSON.parse(JSON.stringify(props.chartData));
		getCharts(chatProps.value);
		createChart();
	});
});
defineExpose({
	reSetChart,
});
</script>

<style scoped lang="scss"></style>
