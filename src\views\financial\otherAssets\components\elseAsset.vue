<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="elseAsset202503151720"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				:isAsc="false"
				:order-by="'assetDate'"
				:queryApi="GetOtherAsset"
				v-loading="loading"
				isNeedDisposeProps
				@disposeProps="disposeProps"
			>
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
				<template #yearFeature="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.yearFeature" :min="-99999999" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.yearFeature) }}</span>
				</template>
				<template #monthFeature="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.monthFeature" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.monthFeature) }}</span>
				</template>
				<template #promiseAmount="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.promiseAmount" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.promiseAmount) }}</span>
				</template>
				<template #depreciation="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.depreciation" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.depreciation) }}</span>
				</template>
				<template #regularPeoperty="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.regularPeoperty" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.regularPeoperty) }}</span>
				</template>
				<template #daiTanDayAmount="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.daiTanDayAmount" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.daiTanDayAmount) }}</span>
				</template>
				<template #daiTanDayBalance="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.daiTanDayBalance" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.daiTanDayBalance) }}</span>
				</template>
				<template #pettyCash="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.pettyCash" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.pettyCash) }}</span>
				</template>
				<template #insideComeReceive="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.insideComeReceive" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.insideComeReceive) }}</span>
				</template>
				<template #otherReceive="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.otherReceive" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.otherReceive) }}</span>
				</template>
				<template #regularOriginalValue="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.regularOriginalValue" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.regularOriginalValue) }}</span>
				</template>
				<template #daiTanOriginalValue="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.daiTanOriginalValue" :min="0" :max="99999999" :controls="false" :precision="2" placeholder="请输入" />
					<span v-else>{{ formatFixedAmt(row.daiTanOriginalValue) }}</span>
				</template>
				<template #saleTransit="{ row }">
					<el-input-number style="width: 100%" v-if="row.statusVerify" v-model.trim="row.saleTransit" :min="0" :max="99999999999" :controls="false" :precision="2" placeholder="请输入" />
					<el-button v-else @click="onInTransitDetails(row)" type="text">{{ formatFixedAmt(row.saleTransit) }}</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog title="在途明细" v-model="showRefundDialog" width="45%" draggable overflow style="margin-top: -30vh !important">
		<div style="height: 100px">
			<el-table :data="subTableData" style="width: 100%" tooltip-effect="light" row-key="id" border="">
				<el-table-column prop="zrsaleTransit" label="昨日销售在途" width="130" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.zrsaleTransit) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="payAmount" label="实发金额" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.payAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="currentReturnAmount" label="当期退货金额" width="130" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.currentReturnAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="totalGoodsIncome" label="平台总收入" width="130" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.totalGoodsIncome) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="jxServiceAmount" label="京喜直营服务费" width="130" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.jxServiceAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="saleTransit" label="销售在途" width="130" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.saleTransit) }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { GetOtherAsset, ExportOtherAsset, EditOtherAsset, GetWarehouseAsset } from '/@/api/cwManager/cwFundsDailyBalance';
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
import { formatters } from '/@/utils/vxetableFormats';
import { ElMessageBox, ElMessage, FormRules } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const loading = ref(false);
const tableData = ref<any[]>([]);
const subTableData = ref<any[]>([]);
const showRefundDialog = ref(false);
const backupTableData = ref<any[]>([]);
const timeRange = ref('');
const table = ref();
const query = ref({
	startDate: '',
	endDate: '',
});

const formatFixedAmt = (value: number) => {
	return value ? formatters.fmtAmt2(value) : value;
};

const findRowIndex = (row: any) => {
	return tableData.value.findIndex((item: any) => item.assetDate === row.assetDate);
};

const handleEdit = (row: any) => {
	const index = findRowIndex(row);
	tableData.value[index].statusVerify = true;
	table.value.onAssignedData(tableData.value);
};

const handleSave = (row: any) => {
	const index = findRowIndex(row);
	tableData.value[index].statusVerify = false;
	table.value.onAssignedData(tableData.value);
	// 删除备份字段
	BACKUP_FIELDS.forEach((field) => {
		delete row[`${field}_Backup`];
	});
	EditOtherAsset({ ...row }).then(() => {
		backupTableData.value = tableData.value;
		ElMessage.success('保存成功');
		// 创建新的备份
		BACKUP_FIELDS.forEach((field) => {
			tableData.value[index][`${field}_Backup`] = tableData.value[index][field];
		});
		table.value.getList();
	});
};

const handleCancel = (row: any) => {
	const index = findRowIndex(row);
	tableData.value[index].statusVerify = false;
	// 恢复备份的值
	BACKUP_FIELDS.forEach((field) => {
		tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
	});
	table.value.onAssignedData(tableData.value);
};

const exportProps = async () => {
	loading.value = true;
	await ExportOtherAsset({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '其他资产导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const BACKUP_FIELDS = ['yearFeature', 'monthFeature', 'promiseAmount', 'depreciation', 'regularPeoperty', 'daiTanDayAmount', 'daiTanDayBalance'] as const;

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.assetDate = dayjs(item.assetDate).format('YYYY-MM-DD');
		item.statusVerify = false;
		// 创建备份
		BACKUP_FIELDS.forEach((field) => {
			item[`${field}_Backup`] = item[field];
		});
	});
	tableData.value = data.data.list;
	backupTableData.value = JSON.parse(JSON.stringify(data.data.list));
	callback(data);
};

const formatThousands = (value: number | null | undefined) => {
	if (value === null || value === undefined) return '0';
	const numStr = typeof value === 'number' ? value.toFixed(2) : String(value);
	return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const onInTransitDetails = (row: any) => {
	const time = row.assetDate ? dayjs(row.assetDate).subtract(1, 'day').format('YYYY-MM-DD') : '';
	GetWarehouseAsset({ startDate: time, endDate: time }).then((res: any) => {
		subTableData.value = [];
		showRefundDialog.value = true;
		if (res.data.list.length > 0) {
			subTableData.value = [
				{
					zrsaleTransit: res.data.list[0].saleTransit,
					payAmount: row.payAmount,
					currentReturnAmount: row.currentReturnAmount,
					totalGoodsIncome: row.totalGoodsIncome,
					jxServiceAmount: row.jxServiceAmount,
					saleTransit: row.saleTransit,
				},
			];
		}
	});
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'assetDate', title: '日期', width: '140' },
	{ sortable: true, field: 'yearFeature', title: '年度预存', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'yearFeatureTotal', title: '年度预存汇总', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'monthFeature', title: '月度预存', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'monthFeatureTotal', title: '月度预存汇总', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{
		title: '其他应收款',
		align: 'center',
		children: [
			{ sortable: true, field: 'promiseAmount', title: '房租押金', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'pettyCash', title: '备用金', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'insideComeReceive', title: '内部往来', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'otherReceive', title: '其他', width: '100', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{
		title: '固定资产',
		align: 'center',
		children: [
			{ sortable: true, field: 'regularOriginalValue', title: '原值', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'depreciation', title: '每日折旧', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'regularPeoperty', title: '净值', width: '100', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{
		title: '待摊费用',
		align: 'center',
		children: [
			{ sortable: true, field: 'daiTanOriginalValue', title: '原值', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'daiTanDayAmount', title: '每日摊销', width: '100', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'daiTanDayBalance', title: '净值', width: '100', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{ sortable: true, field: 'inventory', title: '国内库存', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'buyTransit', title: '采购在途', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'saleTransit', title: '销售在途', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'deliveryBalance', title: ' 快递面单余额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{
		title: '操作',
		width: '130',
		fixed: 'right',
		align: 'center',
		type: 'btnList',
		field: '20250608095341',
		btnList: [
			{ title: '编辑', handle: handleEdit, isDisabled: (row) => row.statusVerify },
			{ title: '保存', handle: handleSave, isDisabled: (row) => !row.statusVerify },
			{ title: '取消', handle: handleCancel, isDisabled: (row) => !row.statusVerify },
		],
	},
]);

onMounted(() => {});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
