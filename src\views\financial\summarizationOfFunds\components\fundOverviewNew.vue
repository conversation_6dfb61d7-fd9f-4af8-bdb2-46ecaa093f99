<template>
	<Container style="width: 100%; height: 100%" v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end" style="width: 240px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="initProps">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<!-- <div class="title_Css">资金趋势</div> -->
			<lineChart
				style="height: 350px"
				v-if="!loading"
				:chartData="trendChart.analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '350px',
					'box-sizing': 'border-box',
					'line-height': '350px',
				}"
				:clickConfig="{
					enabled: true,
					nameField: 'seriesName',
					targetValues: ['总现金', '冻结资金', '总经办'],
				}"
				@onLegendMethod="onLegendMethod"
				@onBarClick="onBarClick"
				:lengendObject="lengendObject"
			/>
			<div class="analysis-boxes-container">
				<div class="analysis-box" v-for="(config, index) in boxConfigs" :key="index">
					<div class="info-panel">
						<div class="panel-header">
							<span class="panel-title">{{ formatReceiptDate(config.data.receiptDate) || query.end }}{{ config.title }}:</span>
							<span class="panel-total" @click="openDetailDialog(config)" :style="{ cursor: 'pointer' }">{{ (onTotalingMethod(config.data) || 0).toLocaleString() }}</span>
						</div>
						<div class="panel-divider"></div>
						<div class="panel-content">
							<div class="content-item" v-for="(item, key) in config.items" :key="key">
								<span class="item-label">
									{{ item.label }}
								</span>
								<span class="item-value">{{ (config.data[item.field] || 0).toLocaleString() }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 详情对话框 -->
			<el-dialog
				v-model="detailDialog.visible"
				:title="detailDialog.title"
				width="70%"
				draggable
				overflow
				@close="closeDetailDialog"
				class="fund-detail-dialog"
				:class="`fund-detail-dialog--${detailDialog.type}`"
			>
				<div class="fund-detail-content">
					<div class="fund-detail-header">
						<dataRange
							v-model:startDate="detailDialog.dateRange.start"
							v-model:endDate="detailDialog.dateRange.end"
							:clearable="false"
							startPlaceholder="开始时间"
							endPlaceholder="结束时间"
							style="width: 260px"
							@change="onDetailDateChange"
							class="fund-detail-date-picker"
						/>
					</div>
					<div class="fund-detail-chart" v-loading="detailDialog.loading">
						<lineChart
							v-if="detailDialog.visible && detailDialog.chartData"
							:chartData="detailDialog.chartData"
							ref="detailChart"
							:thisStyle="{
								width: '100%',
								height: '600px',
								'box-sizing': 'border-box',
								'line-height': '600px',
							}"
							@onLegendMethod="onDetailLegendMethod"
							:lengendObject="detailDialog.legendObject"
							class="fund-detail-line-chart"
						/>
					</div>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { onMounted, ref, computed, defineAsyncComponent, nextTick } from 'vue';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import {
	QueryFundsAnalysis,
	QueryFundsAnalysisByTotalAmount,
	QueryFundsAnalysisByFreezeFund,
	QueryFundsAnalysisByManagerFund,
	QueryFundsAnalysisByTotalAmountByDate,
	QueryFundsAnalysisByFreezeFundByDate,
	QueryFundsAnalysisByManagerFundByDate,
} from '/@/api/financewh/fundsAnalysis';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import dayjs from 'dayjs';
const query = ref({
	start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
	end: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
});
import { decimal } from '/@/utils/decimal';

const sumChart = ref();
const detailChart = ref();
const loading = ref(true);
const lengendObject = ref({});

const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

// 详情对话框数据
const detailDialog = ref({
	visible: false,
	title: '',
	type: '', // 'total', 'freeze', 'manager', 'usable'
	loading: false,
	chartData: null,
	legendObject: {},
	dateRange: {
		start: '',
		end: '',
	},
});

const analysisData5 = ref<any>({});
const analysisData6 = ref<any>({});
const analysisData7 = ref<any>({});

// 盒子配置
const boxConfigs = computed(() => [
	{
		title: '总现金',
		data: analysisData5.value,
		items: [
			{ label: '货款资金', field: 'hk_Amount' },
			{ label: '提现账户余额', field: 'withdrawBalance' },
			{ label: '出纳余额', field: 'chunaBalance' },
			{ label: '对公余额', field: 'duiGongBalance' },
		],
	},
	{
		title: '总经办',
		data: analysisData6.value,
		items: [
			{ label: '提现自用累计', field: 'perWithdraw' },
			{ label: '货款金额', field: 'loanBalance' },
			{ label: '推广费预留', field: 'promotionFeeReservation' },
			{ label: '应付款', field: 'payable' },
			{ label: '冻结资金', field: 'freezeFund' },
			{ label: '启动资金', field: 'enableFund' },
			{ label: '预存资金', field: 'yuCunFund' },
		],
	},
	{
		title: '冻结资金',
		data: analysisData7.value,
		items: [
			{ label: '分销冻结资金', field: 'fx_FreezeFund' },
			{ label: '平台冻结资金', field: 'platFreezeFund' },
			{ label: '平台新增冻结资金', field: 'platNewFreezeFund' },
			{ label: '平台解除冻结资金', field: 'platUnFreezeFund' },
		],
	},
]);

const excludedKeys = ['receiptDate'];
const onTotalingMethod = (Subject: Record<string, any>) => {
	let total = 0;
	for (const key in Subject) {
		if (Object.prototype.hasOwnProperty.call(Subject, key)) {
			if (excludedKeys.includes(key)) continue;
			const value = Number(Subject[key]);
			if (!isNaN(value)) {
				total += Number(decimal(value, 0, 2, '+'));
			}
		}
	}
	return Number(total.toFixed(2));
};

const formatReceiptDate = (receiptDate: any) => {
	if (!receiptDate) return '';
	return dayjs(receiptDate, 'YYYYMMDD').format('YYYY-MM-DD');
};

// 明细API映射
const detailApiMap: Record<string, any> = {
	总现金: QueryFundsAnalysisByTotalAmount,
	冻结资金: QueryFundsAnalysisByFreezeFund,
	总经办: QueryFundsAnalysisByManagerFund,
};

// 打开明细弹窗
const openDetailDialog = async (config: any) => {
	const api = detailApiMap[config.title];
	if (!api) {
		return;
	}

	// 设置对话框基本信息
	detailDialog.value.title = `${config.title}详情`;
	detailDialog.value.type = typeMap[config.title];
	detailDialog.value.visible = true;
	detailDialog.value.loading = true;

	// 设置日期范围：结束日期为query.end，开始日期往前推一个月
	const endDate = query.value.end;
	const startDate = dayjs(endDate).subtract(1, 'month').format('YYYY-MM-DD');

	detailDialog.value.dateRange = {
		start: startDate,
		end: endDate,
	};

	// 调用对应的API
	await loadDetailData(config.title);
};

const onLegendMethod = (val: any) => {
	lengendObject.value = val;
};

// 详情对话框图例方法
const onDetailLegendMethod = (val: any) => {
	detailDialog.value.legendObject = val;
};

// API映射
const apiMap: Record<string, any> = {
	总现金: QueryFundsAnalysisByTotalAmount,
	冻结资金: QueryFundsAnalysisByFreezeFund,
	总经办: QueryFundsAnalysisByManagerFund,
};

// 类型映射
const typeMap: Record<string, string> = {
	总现金: 'total',
	冻结资金: 'freeze',
	总经办: 'manager',
};

// 柱状图点击事件处理
const onBarClick = async (clickData: any) => {
	const clickedName = clickData.name;
	const clickedDataIndex = clickData.dataIndex;

	// 检查是否是我们要处理的系列
	if (!['总现金', '冻结资金', '总经办'].includes(clickedName)) {
		return;
	}

	// 获取点击位置对应的x轴值
	const xAxisData = trendChart.value.analysisData?.xAxis;
	if (!xAxisData || !xAxisData[clickedDataIndex]) {
		return;
	}

	const clickedDate = xAxisData[clickedDataIndex];
	// 将日期从 YYYYMMDD 格式转换为 YYYY-MM-DD 格式
	const formattedDate = dayjs(clickedDate, 'YYYYMMDD').format('YYYY-MM-DD');

	// 重新调用数据获取方法刷新面板数据，传入点击的日期
	try {
		await Promise.all([getProp5(formattedDate), getProp6(formattedDate), getProp7(formattedDate)]);
	} catch (error) {
		console.error('刷新面板数据失败:', error);
	}
};

// 加载详情数据
const loadDetailData = async (clickedName: string) => {
	try {
		detailDialog.value.loading = true;
		const api = apiMap[clickedName];
		const params = {
			start: detailDialog.value.dateRange.start,
			end: detailDialog.value.dateRange.end,
		};

		const res = await api(params);

		// 根据不同的资金类型处理数据
		const processedData = processChartData(res, clickedName);
		detailDialog.value.chartData = processedData;

		// 重置图表
		nextTick(() => {
			if (detailChart.value) {
				detailChart.value.reSetChart(processedData);
			}
		});
	} catch (error) {
		console.error('加载详情数据失败:', error);
	} finally {
		detailDialog.value.loading = false;
	}
};

// 处理图表数据
const processChartData = (data: any, clickedName: string) => {
	const processedData = JSON.parse(JSON.stringify(data)); // 深拷贝避免修改原数据

	if (!processedData.series || !Array.isArray(processedData.series)) {
		return processedData;
	}

	// 总现金、冻结资金使用折线图
	if (clickedName === '总现金' || clickedName === '冻结资金') {
		processedData.series.forEach((item: any) => {
			item.type = 'line';
			item.smooth = true;
		});
	}
	// 总经办使用堆叠柱状图
	else if (clickedName === '总经办') {
		processedData.series.forEach((item: any) => {
			item.stack = 'Total';
			item.type = 'bar';
		});
	}

	return processedData;
};

// 详情日期变化处理
const onDetailDateChange = () => {
	const currentType = Object.keys(typeMap).find((key) => typeMap[key] === detailDialog.value.type);
	if (currentType) {
		loadDetailData(currentType);
	}
};

// 关闭详情对话框
const closeDetailDialog = () => {
	detailDialog.value.visible = false;
	detailDialog.value.chartData = null;
	detailDialog.value.legendObject = {};
};

const getProp = async () => {
	const res = await QueryFundsAnalysis(query.value);
	trendChart.value.analysisData = res;
	trendChart.value.analysisData?.series.forEach((item: any) => {
		if (item.type == 'line') {
			item.smooth = true;
		}
	});
	trendChart.value.analysisData.title = '资金概览';
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};

const getProp5 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryFundsAnalysisByTotalAmountByDate({ date: dateToUse });
		if (res) {
			analysisData5.value = res;
		} else {
			analysisData5.value = {
				hk_Amount: 0,
				withdrawBalance: 0,
				chunaBalance: 0,
				duiGongBalance: 0,
			};
		}
	} catch (error) {
		console.error('获取总收入数据失败:', error);
	}
};

const getProp6 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryFundsAnalysisByManagerFundByDate({ date: dateToUse });
		if (res) {
			analysisData6.value = res;
		} else {
			analysisData6.value = {
				perWithdraw: 0,
				loanBalance: 0,
				promotionFeeReservation: 0,
				otherOut: 0,
			};
		}
	} catch (error) {
		console.error('获取总支出数据失败:', error);
	}
};

const getProp7 = async (customDate?: string) => {
	try {
		const dateToUse = customDate || query.value.end;
		const res = await QueryFundsAnalysisByFreezeFundByDate({ date: dateToUse });
		if (res) {
			analysisData7.value = res;
		} else {
			analysisData7.value = {
				fx_FreezeFund: 0,
				platFreezeFund: 0,
				platNewFreezeFund: 0,
				platUnFreezeFund: 0,
			};
		}
	} catch (error) {
		console.error('获取冻结资金数据失败:', error);
	}
};

const initProps = () => {
	loading.value = true;
	Promise.all([getProp(), getProp5(), getProp6(), getProp7()])
		.then(() => {
			loading.value = false;
		})
		.finally(() => {
			loading.value = false;
		});
};
onMounted(async () => {
	initProps();
});
</script>

<style scoped lang="scss">
#main {
	width: 100%;
	height: 100%;
}

// 资金详情对话框样式
.fund-detail-dialog {
	:deep(.el-dialog__header) {
		padding: 20px 20px 10px;
		border-bottom: 1px solid #ebeef5;
	}

	:deep(.el-dialog__body) {
		padding: 20px;
	}

	// 不同类型的对话框可以有不同的样式
	&.fund-detail-dialog--total {
		:deep(.el-dialog__header) {
			background-color: #f0f9ff;
		}
	}

	&.fund-detail-dialog--freeze {
		:deep(.el-dialog__header) {
			background-color: #fef3f2;
		}
	}

	&.fund-detail-dialog--manager {
		:deep(.el-dialog__header) {
			background-color: #f6ffed;
		}
	}

	&.fund-detail-dialog--usable {
		:deep(.el-dialog__header) {
			background-color: #fff7e6;
		}
	}
}

.fund-detail-content {
	width: 100%;

	.fund-detail-header {
		margin-bottom: 20px;
		padding-bottom: 15px;
		border-bottom: 1px solid #f0f0f0;

		.fund-detail-date-picker {
			:deep(.el-date-editor) {
				width: 100%;
			}
		}
	}

	.fund-detail-chart {
		width: 100%;
		min-height: 600px;

		.fund-detail-line-chart {
			width: 100%;
			height: 100%;
		}
	}
}

.title_Css {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
}

// 三个水平排列盒子的样式
.analysis-boxes-container {
	display: flex;
	flex-direction: row;
	gap: 20px;
	height: 100%;
	padding: 20px 0;
	overflow-x: auto;
}

.analysis-box {
	display: flex;
	flex-direction: column;
	flex: 1;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
		transform: translateY(-2px);
	}

	.info-panel {
		background-color: #ffffff;
		color: #333333;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		&:hover {
			background-color: #b0cceb;
			color: #000000;
		}

		.panel-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 20px 10px;
			flex-shrink: 0;
			background-color: inherit;

			.panel-title {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				line-height: 1.4;
				flex-shrink: 0;
			}

			.panel-total {
				font-size: 16px;
				font-weight: 600;
				color: #409eff;
				transition: all 0.3s ease;

				&:hover {
					color: #1890ff;
					transform: scale(1.05);
				}
			}
		}

		.panel-divider {
			height: 1px;
			background-color: #e4e7ed;
			margin: 0 20px 8px;
			flex-shrink: 0;
		}

		.panel-content {
			flex: 1;
			overflow-y: auto;
			padding: 0 20px 16px;
			max-height: 290px;
			height: 290px;

			.content-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;
				line-height: 1.4;

				&:last-child {
					margin-bottom: 0;
				}

				.item-label {
					font-weight: 600;
					color: #333333;
					flex-shrink: 0;
				}

				.item-value {
					font-weight: normal;
					color: #666666;
					text-align: right;
				}
			}
		}
	}

	.chart-panel {
		background-color: #ffffff;
		padding: 16px;
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;

		.no-data {
			color: #999999;
			font-size: 14px;
			text-align: center;
		}
	}
}
</style>
