import type { App } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { judgementSameArr } from '/@/utils/arrayOperation';

/**
 * 用户权限指令
 * @directive 单个权限验证（v-auth="xxx"）
 * @directive 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
 * @directive 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
 */
export function authDirective(app: App) {
	const getPermList = (): string[] => {
		const stores = useUserInfo();
		const list = (stores?.userInfos as any)?.authBtnList ?? (stores?.userInfos as any)?.permissions ?? [];
		return Array.isArray(list) ? (list as string[]) : [];
	};
	// 单个权限验证（v-auth="xxx"）
	app.directive('auth', {
		mounted(el, binding) {
			if (binding.value === '' || binding.value === null || binding.value === undefined) return;
			const list = getPermList();
			if (!Array.isArray(binding.value)) {
				if (!list.some((v: string) => v === binding.value)) el.parentNode && el.parentNode.removeChild(el);
			} else if (Array.isArray(binding.value)) {
				let flag = false;
				for (const val of list) {
					if (binding.value.includes(val)) {
						flag = true;
						break;
					}
				}
				if (!flag) el.parentNode && el.parentNode.removeChild(el);
			}
		},
	});
	// 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
	app.directive('auths', {
		mounted(el, binding) {
			let flag = false;
			const list = getPermList();
			if (Array.isArray(binding.value)) {
				for (const val of list) {
					if (binding.value.includes(val)) {
						flag = true;
						break;
					}
				}
			}
			if (!flag) el.parentNode && el.parentNode.removeChild(el);
		},
	});
	// 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
	app.directive('auth-all', {
		mounted(el, binding) {
			const list = getPermList();
			const flag = Array.isArray(binding.value) ? judgementSameArr(binding.value, list) : false;
			if (!flag) el.parentNode && el.parentNode.removeChild(el);
		},
	});
}
